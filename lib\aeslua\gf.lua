-- Galois Field operations for AES
local bit = require("bit")

local public = {}

-- G<PERSON><PERSON> Field multiplication by 2
function public.mul2(a)
    if bit.band(a, 0x80) == 0 then
        return bit.lshift(a, 1)
    else
        return bit.bxor(bit.lshift(a, 1), 0x1b)
    end
end

-- Galois Field multiplication by 3
function public.mul3(a)
    return bit.bxor(public.mul2(a), a)
end

-- Galois Field multiplication by 9
function public.mul9(a)
    return bit.bxor(public.mul2(public.mul2(public.mul2(a))), a)
end

-- Galois Field multiplication by 11 (0x0b)
function public.mul11(a)
    return bit.bxor(bit.bxor(public.mul2(public.mul2(public.mul2(a))), public.mul2(a)), a)
end

-- Galois Field multiplication by 13 (0x0d)
function public.mul13(a)
    return bit.bxor(bit.bxor(public.mul2(public.mul2(public.mul2(a))), public.mul2(public.mul2(a))), a)
end

-- <PERSON><PERSON><PERSON> Field multiplication by 14 (0x0e)
function public.mul14(a)
    return bit.bxor(bit.bxor(public.mul2(public.mul2(public.mul2(a))), public.mul2(public.mul2(a))), public.mul2(a))
end

-- General Galois Field multiplication
function public.mul(a, b)
    local result = 0
    local temp_a = a
    local temp_b = b
    
    while temp_b ~= 0 do
        if bit.band(temp_b, 1) == 1 then
            result = bit.bxor(result, temp_a)
        end
        
        temp_a = public.mul2(temp_a)
        temp_b = bit.rshift(temp_b, 1)
    end
    
    return result
end

-- Galois Field division (multiplication by inverse)
function public.div(a, b)
    if b == 0 then
        error("Division by zero in Galois Field")
    end
    
    -- Find multiplicative inverse of b
    local inv = public.inverse(b)
    return public.mul(a, inv)
end

-- Find multiplicative inverse in GF(2^8)
function public.inverse(a)
    if a == 0 then
        return 0
    end
    
    -- Extended Euclidean algorithm in GF(2^8)
    local u = a
    local v = 0x11b  -- AES irreducible polynomial x^8 + x^4 + x^3 + x + 1
    local g1 = 1
    local g2 = 0
    
    while u ~= 1 do
        local j = public.degree(u) - public.degree(v)
        
        if j < 0 then
            u, v = v, u
            g1, g2 = g2, g1
            j = -j
        end
        
        u = bit.bxor(u, bit.lshift(v, j))
        g1 = bit.bxor(g1, bit.lshift(g2, j))
    end
    
    return g1
end

-- Calculate degree of polynomial (position of highest bit)
function public.degree(a)
    local degree = -1
    local temp = a
    
    while temp ~= 0 do
        degree = degree + 1
        temp = bit.rshift(temp, 1)
    end
    
    return degree
end

return public
