function string.fromhex(str)
    return (str:gsub('..', function (cc)
        return string.char(tonumber(cc, 16))
    end))
end

_G.decrypt = function(code,key_type)
    local key = "HJB2m9GP"
    local text = string.fromhex(code)
    if key_type == 1 then
        key = "QLk4icW5aHJB2m9GPqLvypcd1gzMbEcfrvczlqPF"
    end
    local plain = aeslua.decrypt(key, text, aeslua.AES128, aeslua.CBCMODE)
    return plain
end

