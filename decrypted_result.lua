LinkLuaModifier( "modifier_assist_ability_centaur", "abilities/assist_card/assist_ability_centaur", LUA_MODIFIER_MOTION_NONE )
LinkLuaModifier( "modifier_summon_attack", "modifiers/summon/modifier_summon_attack", LUA_MODIFIER_MOTION_NONE )
LinkLuaModifier("modifier_generic_knockback_lua", "abilities/generic/modifier_generic_knockback_lua", LUA_MODIFIER_MOTION_BOTH)

assist_ability_centaur = class({})

function assist_ability_centaur:GetCastAnimation()
    local animation=Kv_Cache.hero_animation[self:GetCaster():GetUnitName()][RandomInt(1,2)]
    return animation
end 

function assist_ability_centaur:OnSpellStart()
    if not IsServer() then   
        return 
    end 

    local point=self:GetCursorPosition()
    local caster=self:GetCaster()
    
    local caster_point=caster:GetAbsOrigin()
    local damage=self:GetSpecialValueFor("damage")
    local health=self:GetSpecialValueFor("health")
    local ill_name="assist_centaur"

    if self:GetLevel()==2 then 
        ill_name="assist_centaur_1"
    end 
    if self:GetLevel()==3 then 
        ill_name="assist_centaur_2"
    end 

    local ill = CreateUnitByName(ill_name, point, true, caster, nil, caster:GetTeamNumber())

    local spawn_sound="centaur_cent_spawn_0"..tostring(RandomInt(1,3))
    EmitSoundOn(spawn_sound, ill)

    local center_point_x=BattleBoard:GetPlayerBattleInfo_Center(self:GetCaster():GetPlayerOwnerID()).x

    local vDirection=""
    if caster:GetAbsOrigin().x >center_point_x then  

        vDirection = Vector(point.x-100,point.y,point.z) - point
        vDirection.z = 0
    else 
        vDirection = Vector(point.x+100,point.y,point.z) - point
        vDirection.z = 0
    end 

    ill:SetForwardVector(vDirection)   
    FindClearSpaceForUnit( ill, point, true )
    ill:SetOwner(caster)

    ill:SetMaxHealth(health)
    ill:SetBaseMaxHealth(health)
    ill:SetHealth(health)
    ill:SetBaseDamageMax(damage)
    ill:SetBaseDamageMin(damage)
    ill:AddNewModifier(ill,self,"modifier_summon_attack", {} )

    ill:AddNewModifier( caster, self, "modifier_rooted", {duration=0.5} )
    ill:AddNewModifier(caster,self,"modifier_assist_ability_centaur", {})
    ill:AddActivityModifier('ti9_weapon')
end 
---------------------------------------------------------------------------------------
modifier_assist_ability_centaur = class({})
function modifier_assist_ability_centaur:IsHidden() 
	return true
end
function modifier_assist_ability_centaur:IsPurgable()
	return false
end
function modifier_assist_ability_centaur:IsDebuff()
    return false 
end 

function modifier_assist_ability_centaur:GetEffectName() 
    return "particles/units/heroes/hero_centaur/centaur_stampede.vpcf"
end 

function modifier_assist_ability_centaur:GetEffectAttachType()
    return PATTACH_ABSORIGIN_FOLLOW
end 

function modifier_assist_ability_centaur:OnCreated( kv )
    self.cooldown= 8
    self.damage_per=9
    self.max_stack=5

    if self:GetAbility() then 
        self.cooldown= self:GetAbility():GetSpecialValueFor( "cooldown" )
        self.damage_per= self:GetAbility():GetSpecialValueFor( "damage_per" )
        self.max_stack= self:GetAbility():GetSpecialValueFor( "max_stack" )
    end
end

function modifier_assist_ability_centaur:OnRefresh( kv )
    self.cooldown= 8
    self.damage_per=9
    self.max_stack=5

    if self:GetAbility() then 
        self.cooldown= self:GetAbility():GetSpecialValueFor( "cooldown" )
        self.damage_per= self:GetAbility():GetSpecialValueFor( "damage_per" )
        self.max_stack= self:GetAbility():GetSpecialValueFor( "max_stack" )
    end
end

function modifier_assist_ability_centaur:DeclareFunctions()
	local funcs = 
	{
        MODIFIER_EVENT_ON_ATTACK_START,
        MODIFIER_EVENT_ON_ATTACK_LANDED,
        MODIFIER_PROPERTY_BASEDAMAGEOUTGOING_PERCENTAGE,
	}
	return funcs
end

function modifier_assist_ability_centaur:GetModifierBaseDamageOutgoing_Percentage()
    return self.damage_per*self:GetStackCount()
end 

function modifier_assist_ability_centaur:OnAttackStart( params )
	if IsServer() then
		local hTarget = params.target
		local hAttacker = params.attacker
		local hCaster = self:GetParent()

		if hAttacker ~=  hCaster then return end
		if params.inflictor ~= nil then return end
        hAttacker:StartGesture(ACT_DOTA_CAST_ABILITY_2)
        EmitSoundOn("Hero_Centaur.DoubleEdge", hAttacker)
        self.phase_double_edge_pfx = ParticleManager:CreateParticle("particles/econ/items/centaur/centaur_ti9/centaur_double_edge_phase_ti9.vpcf", PATTACH_CUSTOMORIGIN, hAttacker)
		ParticleManager:SetParticleControl(self.phase_double_edge_pfx, 0, hAttacker:GetAbsOrigin())
		ParticleManager:SetParticleControl(self.phase_double_edge_pfx, 3, hAttacker:GetAbsOrigin())
		ParticleManager:SetParticleControl(self.phase_double_edge_pfx, 9, hAttacker:GetAbsOrigin())
    end
end  

function modifier_assist_ability_centaur:OnAttackLanded( params )
	if IsServer() then
		local hTarget = params.target
		local hAttacker = params.attacker
		local hCaster = self:GetParent()

		if hAttacker ~=  hCaster then return end
		if params.inflictor ~= nil then return end

        ParticleManager:DestroyParticle(self.phase_double_edge_pfx, false)
		ParticleManager:ReleaseParticleIndex(self.phase_double_edge_pfx)
        -- EmitSoundOn("Hero_Centaur.DoubleEdge",hTarget)
			
        -- local effect = "particles/units/heroes/hero_centaur/centaur_double_edge.vpcf"
        -- local pfx = ParticleManager:CreateParticle(effect, PATTACH_ABSORIGIN_FOLLOW, hTarget)
        -- ParticleManager:SetParticleControl(pfx, 0, hCaster:GetAbsOrigin()) -- Origin
        -- ParticleManager:SetParticleControl(pfx, 1, hTarget:GetAbsOrigin()) -- Destination
        -- ParticleManager:SetParticleControl(pfx, 5, hTarget:GetAbsOrigin()) -- Hit Glow

        EmitSoundOn("Hero_PhantomAssassin.Arcana_Layer", hTarget)
	
        local offset = 200
        local origin = hAttacker:GetOrigin()
        local direction_normalized = (hTarget:GetOrigin() - origin):Normalized()
        local final_position = origin + Vector(direction_normalized.x * offset, direction_normalized.y * offset, 0)
    
        local particle_cast = "particles/econ/items/centaur/centaur_ti9/centaur_double_edge_ti9.vpcf"
        EmitSoundOn("Hero_PhantomAssassin.Spatter", hTarget)
    
        local effect_cast = ParticleManager:CreateParticle(particle_cast, PATTACH_ABSORIGIN_FOLLOW, hTarget)
        ParticleManager:SetParticleControl(effect_cast, 2, hTarget:GetAbsOrigin())
        ParticleManager:SetParticleControl(effect_cast, 3, hTarget:GetAbsOrigin())
        ParticleManager:SetParticleControlForward(effect_cast, 1, (origin - final_position):Normalized())
        ParticleManager:ReleaseParticleIndex(effect_cast)
    

        local direction=hTarget:GetAbsOrigin()-self:GetParent():GetAbsOrigin()
        direction.z=0

        hTarget:AddNewModifier(
            self:GetParent(), -- player source
            self, -- ability source
            "modifier_generic_knockback_lua", -- modifier name
            {
                duration = 0.15,
                distance = 75,
                height   = 0,
                direction_x = direction.x,
                direction_y = direction.y,
                IsFlail = false,
            } -- kv
        )

        if self:GetAbility():GetLevel()>=2 then    
            if self:GetStackCount()<self.max_stack then 
                self:IncrementStackCount()
                if self:GetStackCount()==1 then 
                    self.shard_pfx = ParticleManager:CreateParticle("particles/units/heroes/hero_centaur/centaur_shard_buff_strength.vpcf", PATTACH_ABSORIGIN_FOLLOW, self:GetParent())
                    ParticleManager:SetParticleControl(self.shard_pfx, 0, self:GetParent():GetAbsOrigin())
                    ParticleManager:SetParticleControlEnt(self.shard_pfx, 1, self:GetParent(), PATTACH_POINT_FOLLOW, "attach_hitloc", self:GetParent():GetAbsOrigin(), true)
                    ParticleManager:SetParticleControl(self.shard_pfx, 2, Vector(self:GetStackCount(), 0, 0))
                    ParticleManager:SetParticleControlEnt(self.shard_pfx, 3, self:GetParent(), PATTACH_OVERHEAD_FOLLOW, "follow_overhead", self:GetParent():GetAbsOrigin(), true)
                    self:AddParticle(self.shard_pfx, false, false, -1, false, false)
                else 
                    ParticleManager:SetParticleControl(self.shard_pfx, 2, Vector(self:GetStackCount(), 0, 0))
                end 
            end 
        end 

        -- Timers:CreateTimer(0.2,function()
        --     hAttacker:FadeGesture(ACT_DOTA_CAST_ABILITY_2)
        --     hAttacker:AddNewModifier( hAttacker, self:GetAbility(), "modifier_stunned", {duration=self.cooldown} )
        -- end)

        -- hAttacker:AddNewModifier( hAttacker, self:GetAbility(), "modifier_rooted", {duration=self.cooldown+0.2} )
        -- Timers:CreateTimer(0.2,function()
        --     hAttacker:FadeGesture(ACT_DOTA_CAST_ABILITY_2)
        --     hAttacker:AddNewModifier( hAttacker, self:GetAbility(), "modifier_disarmed", {duration=self.cooldown} )
        --     hAttacker:StartGesture(ACT_DOTA_DISABLED)
        -- end)

        -- Timers:CreateTimer(0.2+self.cooldown,function()
        --     hAttacker:FadeGesture(ACT_DOTA_DISABLED)
        -- end)
        hAttacker:FadeGesture(ACT_DOTA_CAST_ABILITY_2)
        hAttacker:StartGesture(ACT_DOTA_DISABLED)

        hAttacker:AddNewModifier( hAttacker, self:GetAbility(), "modifier_rooted", {duration=self.cooldown-0.7} )
        hAttacker:AddNewModifier( hAttacker, self:GetAbility(), "modifier_disarmed", {duration=self.cooldown-0.7})

        -- hAttacker:AddNewModifier( hAttacker, self:GetAbility(), "modifier_disarmed", {duration=self.cooldown-0.7})

        Timers:CreateTimer(self.cooldown-0.7,function()
            if not IsValid(hAttacker) then return end 
            hAttacker:FadeGesture(ACT_DOTA_DISABLED)
        end)
    end
end  



