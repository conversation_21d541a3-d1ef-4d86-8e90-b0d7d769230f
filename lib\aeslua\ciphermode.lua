-- AES cipher modes implementation
local aes = require("lib.aeslua.aes")
local util = require("lib.aeslua.util")
local buffer = require("lib.aeslua.buffer")

local public = {}

-- Encrypt string with specified mode
function public.encryptString(key, data, modeFunction)
    local iv = {0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0}
    local keySched = aes.expandEncryptionKey(key)
    local encryptedData = buffer.new()
    
    for i = 1, #data/16 do
        local offset = (i-1)*16 + 1
        local byteData = {string.byte(data, offset, offset + 15)}
        iv = modeFunction(keySched, byteData, iv)
        buffer.addString(encryptedData, string.char(unpack(byteData)))
    end
    
    return buffer.toString(encryptedData)
end

-- Decrypt string with specified mode
function public.decryptString(key, data, modeFunction)
    local iv = {0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0}
    local keySched
    
    -- For decryption modes that need decryption key schedule
    if modeFunction == public.decryptECB or modeFunction == public.decryptCBC then
        local encKeySched = aes.expandEncryptionKey(key)
        keySched = aes.expandDecryptionKey(encKeySched)
    else
        keySched = aes.expandEncryptionKey(key)
    end
    
    local decryptedData = buffer.new()
    
    for i = 1, #data/16 do
        local offset = (i-1)*16 + 1
        local byteData = {string.byte(data, offset, offset + 15)}
        iv = modeFunction(keySched, byteData, iv)
        buffer.addString(decryptedData, string.char(unpack(byteData)))
    end
    
    return buffer.toString(decryptedData)
end

-- ECB mode encryption
function public.encryptECB(keySched, byteData, iv)
    aes.encrypt(keySched, byteData, 1, byteData, 1)
    return iv
end

-- CBC mode encryption
function public.encryptCBC(keySched, byteData, iv)
    util.xorIV(byteData, iv)
    aes.encrypt(keySched, byteData, 1, byteData, 1)
    
    local nextIV = {}
    for j = 1, 16 do
        nextIV[j] = byteData[j]
    end
    return nextIV
end

-- OFB mode encryption
function public.encryptOFB(keySched, byteData, iv)
    aes.encrypt(keySched, iv, 1, iv, 1)
    util.xorIV(byteData, iv)
    return iv
end

-- CFB mode encryption
function public.encryptCFB(keySched, byteData, iv)
    aes.encrypt(keySched, iv, 1, iv, 1)
    util.xorIV(byteData, iv)
    
    local nextIV = {}
    for j = 1, 16 do
        nextIV[j] = byteData[j]
    end
    return nextIV
end

-- ECB mode decryption
function public.decryptECB(keySched, byteData, iv)
    aes.decrypt(keySched, byteData, 1, byteData, 1)
    return iv
end

-- CBC mode decryption
function public.decryptCBC(keySched, byteData, iv)
    local nextIV = {}
    for j = 1, 16 do
        nextIV[j] = byteData[j]
    end
    
    aes.decrypt(keySched, byteData, 1, byteData, 1)
    util.xorIV(byteData, iv)
    
    return nextIV
end

-- OFB mode decryption
function public.decryptOFB(keySched, byteData, iv)
    aes.encrypt(keySched, iv, 1, iv, 1)
    util.xorIV(byteData, iv)
    return iv
end

-- CFB mode decryption
function public.decryptCFB(keySched, byteData, iv)
    local nextIV = {}
    for j = 1, 16 do
        nextIV[j] = byteData[j]
    end
    
    aes.encrypt(keySched, iv, 1, iv, 1)
    util.xorIV(byteData, iv)
    
    return nextIV
end

return public
