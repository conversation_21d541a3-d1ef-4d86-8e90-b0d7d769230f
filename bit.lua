-- Pure Lua bitlib implementation
-- Based on bitlib functionality needed for AES

local bit = {}

-- Number of bits available
bit.bits = 32

-- Cast to integer type
function bit.cast(a)
    if a < 0 then
        return a % (2^32)
    else
        return a % (2^32)
    end
end

-- Bitwise NOT
function bit.bnot(a)
    return (~a) % (2^32)
end

-- Bitwise AND
function bit.band(...)
    local args = {...}
    if #args == 0 then return 0 end
    
    local result = args[1] % (2^32)
    for i = 2, #args do
        result = result & (args[i] % (2^32))
    end
    return result
end

-- Bitwise OR
function bit.bor(...)
    local args = {...}
    if #args == 0 then return 0 end
    
    local result = args[1] % (2^32)
    for i = 2, #args do
        result = result | (args[i] % (2^32))
    end
    return result
end

-- Bitwise XOR
function bit.bxor(...)
    local args = {...}
    if #args == 0 then return 0 end
    
    local result = args[1] % (2^32)
    for i = 2, #args do
        result = result ~ (args[i] % (2^32))
    end
    return result
end

-- Left shift
function bit.lshift(a, b)
    if b < 0 then
        return bit.rshift(a, -b)
    end
    return (a * (2^b)) % (2^32)
end

-- Logical right shift
function bit.rshift(a, b)
    if b < 0 then
        return bit.lshift(a, -b)
    end
    return math.floor(a / (2^b)) % (2^32)
end

-- Arithmetic right shift
function bit.arshift(a, b)
    if b < 0 then
        return bit.lshift(a, -b)
    end
    
    local result = math.floor(a / (2^b))
    -- Handle sign extension for negative numbers
    if a >= 2^31 then
        result = result + (2^32 - 2^(32-b))
    end
    return result % (2^32)
end

return bit
