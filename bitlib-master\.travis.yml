#
# LuaDist Travis<PERSON><PERSON><PERSON> Hook
#

# We assume C build environments
language: C

# Try using multiple Lua Implementations
env:
  - TOOL="gcc"                  # Use native compiler (GCC usually)
  - TOOL="clang"                # Use clang
  - TOOL="i686-w64-mingw32"     # 32bit MinGW
  - TOOL="x86_64-w64-mingw32"   # 64bit MinGW
  - TOOL="arm-linux-gnueabihf"  # ARM hard-float (hf), linux

# Crosscompile builds may fail
matrix:
  allow_failures:
    - env: TOOL="i686-w64-mingw32"
    - env: TOOL="x86_64-w64-mingw32"
    - env: TOOL="arm-linux-gnueabihf"

# Install dependencies
install:
  - git clone git://github.com/LuaDist/Tools.git ~/_tools
  - ~/_tools/travis/travis install

# Bootstap
before_script:
  - ~/_tools/travis/travis bootstrap

# Build the module
script:
  - ~/_tools/travis/travis build

# Execute additional tests or commands
after_script:
  - ~/_tools/travis/travis test

# Only watch the master branch
branches:
  only:
    - master

# Notify the LuaDist Dev group if needed
notifications:
  recipients:
    - <EMAIL>
  email:
    on_success: change
    on_failure: always
