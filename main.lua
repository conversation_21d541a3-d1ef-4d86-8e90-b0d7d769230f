#!/usr/bin/env lua

-- Main script to decode 2.lua
-- Load the required modules
require("1")  -- This loads the decrypt function and string.fromhex
require("aeslua")  -- This loads the AES library

-- Load and execute 2.lua
local result = dofile("3.lua")

print("Decrypted result:")
print(result)

-- Save the result to a file
local file = io.open("decrypted_result.lua", "w")
if file then
    file:write(result)
    file:close()
    print("\nResult saved to decrypted_result.lua")
else
    print("\nFailed to save result to file")
end
