-- Buffer implementation for AES operations

local public = {}

-- Create a new buffer
function public.new()
    return {data = {}}
end

-- Add a string to the buffer
function public.addString(buffer, str)
    table.insert(buffer.data, str)
end

-- Convert buffer to string
function public.toString(buffer)
    return table.concat(buffer.data)
end

-- Add bytes to buffer
function public.addBytes(buffer, bytes)
    local str = ""
    for i = 1, #bytes do
        str = str .. string.char(bytes[i])
    end
    public.addString(buffer, str)
end

-- Get buffer length
function public.getLength(buffer)
    local length = 0
    for i = 1, #buffer.data do
        length = length + #buffer.data[i]
    end
    return length
end

-- Clear buffer
function public.clear(buffer)
    buffer.data = {}
end

return public
